// ============================================================================
// COMPONENTE OBSOLETO - FUNCIONALIDAD DE INACTIVIDAD DESHABILITADA
// ============================================================================
// Este componente ya no se usa en la aplicación. La funcionalidad de desconexión
// automática por inactividad ha sido completamente deshabilitada.
// Se mantiene por compatibilidad pero no se renderiza en ningún lugar.
// ============================================================================

import React, { useState, useEffect } from 'react';
import { FiClock, FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';
import { InactivityWarningProps } from '@/types/ui';

const InactivityWarning: React.FC<InactivityWarningProps> = ({
  isVisible,
  timeRemaining,
  onExtendSession,
  onLogout
}) => {
  const [countdown, setCountdown] = useState(timeRemaining);

  useEffect(() => {
    setCountdown(timeRemaining);
  }, [timeRemaining]);

  useEffect(() => {
    if (!isVisible || countdown <= 0) return;

    const interval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          onLogout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isVisible, countdown, onLogout]);

  if (!isVisible) return null;

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-pulse">
        <div className="flex items-center mb-4">
          <div className="bg-yellow-100 rounded-full p-3 mr-4">
            <FiAlertTriangle className="text-yellow-600 text-xl" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Sesión por expirar
            </h3>
            <p className="text-sm text-gray-600">
              Tu sesión expirará por inactividad
            </p>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-center">
            <FiClock className="text-gray-500 mr-2" />
            <span className="text-2xl font-mono font-bold text-gray-900">
              {formatTime(countdown)}
            </span>
          </div>
          <p className="text-center text-sm text-gray-600 mt-2">
            Tiempo restante antes del cierre automático
          </p>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={onExtendSession}
            className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-colors"
          >
            <FiRefreshCw className="mr-2" />
            Continuar sesión
          </button>
          <button
            onClick={onLogout}
            className="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Cerrar sesión
          </button>
        </div>

        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            Por seguridad, tu sesión se cerrará automáticamente tras 10 minutos de inactividad
          </p>
        </div>
      </div>
    </div>
  );
};

export default InactivityWarning;
